<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

interface Props {
  data: any;
}

const props = defineProps<Props>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 截断文本函数
const truncateText = (text: string, maxLength: number = 10) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const renderChart = () => {
  if (Object.keys(props.data).length === 0) {
    return;
  }

  const pieData = Object.entries(props.data).map(([name, value]) => ({
    name: truncateText(name, 10), // 显示截断后的名称
    value: Number(value),
    originalName: name, // 保存原始名称用于 tooltip 显示
  }));

  renderEcharts({
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const originalName = params.data.originalName || params.name;
        return `${params.seriesName} <br/>${originalName}: ${params.value} (${params.percent}%)`;
      },
    },
    legend: {
      bottom: '0%',
      left: 'center',
      formatter: (name: string) => truncateText(name, 10),
      tooltip: {
        show: true,
        formatter: (params: any) => params.name,
      },
    },
    series: [
      {
        name: '场景分布',
        type: 'pie',
        radius: ['30%', '60%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: pieData,
        color: [
          '#5470c6',
          '#91cc75',
          '#fac858',
          '#ee6666',
          '#73c0de',
          '#3ba272',
          '#fc8452',
        ],
      },
    ],
  });
};

watch(() => props.data, renderChart, { deep: true });

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI style="height: 100%" ref="chartRef" />
</template>
