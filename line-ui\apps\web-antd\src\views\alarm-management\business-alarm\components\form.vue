<script lang="ts" setup>
import { computed, reactive, ref } from 'vue';

import { message, Descriptions, DescriptionsItem } from 'ant-design-vue';

import {
  getBusinessAlarm_Api,
  updateBusinessAlarm_Api,
} from '#/api/core/business-alarm';
import { upload_file } from '#/api/upload';

import { formatDate } from '@vben/utils';

import { useDictStore } from '#/store/dict';

const mediaUrl = import.meta.env.VITE_GLOB_LOAD_ERROR_URL;

const dictStore = useDictStore();

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);
const labelCol = { span: 4 };
const wrapperCol = { span: 20 };

const detailsData = ref<any>({});
const modalType = ref<string>('');

// 表单数据
const formData = reactive<any>({
  id: undefined,
  verifyStatus: undefined, // 处理状态：1-真实报警，2-误报
  handleDesc: undefined, // 处理说明
  handleImages: [], // 图片URL数组
  forbidAlarm: undefined, // 设置不报警：ture-是，false-否
  forbidAlarmTime: undefined, // 解除不报警时间
});

// 上传文件列表（用于Upload组件显示）
const fileList = ref<any[]>([]);

// 表单验证规则
const formRules = {
  verifyStatus: [
    { required: true, message: '请选择处理状态', trigger: 'change' },
  ],
  handleDesc: [{ required: true, message: '请输入处理说明', trigger: 'blur' }],
  handleImages: [{ required: true, message: '请上传图片', trigger: 'change' }],
  forbidAlarm: [
    { required: true, message: '请选择是否设置不报警', trigger: 'change' },
  ],
  forbidAlarmTime: [
    { required: true, message: '请选择解除不报警时间', trigger: 'change' },
  ],
};

// 是否显示处理说明和图片字段
const showDescAndImage = computed(() => formData.verifyStatus === 'REAL');

// 是否显示设置不报警字段
const showNoAlarm = computed(() => formData.verifyStatus === 'FALSE_ALARM');

// 是否显示解除不报警时间字段
const showCancelTime = computed(
  () =>
    formData.forbidAlarm === true && formData.verifyStatus === 'FALSE_ALARM',
);

// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    verifyStatus: undefined,
    handleDesc: undefined,
    handleImages: [],
    forbidAlarm: undefined,
    forbidAlarmTime: undefined,
  });
  fileList.value = [];
};

// 获取告警详情
const getDetailsData = async (id: any) => {
  try {
    const data = await getBusinessAlarm_Api(id);
    detailsData.value = data;
  } catch (error) {
    console.error('获取告警详情失败', error);
    message.error('获取告警详情失败');
  }
};

// 打开弹窗
const openModal = async (options: { type: string; data?: any }) => {
  open.value = true;
  modalType.value = options.type;
  modalTitle.value = options.type === 'toCheck' ? '核实' : '查看';

  await resetForm();

  if (options.data && options.data.id) {
    formData.id = options.data.id;
    await getDetailsData(options.data.id);
  }
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 自定义上传
const customUpload = (options: any) => {
  const { file, onSuccess, onError, onProgress } = options;

  // 模拟上传进度
  onProgress?.({ percent: 0 });

  upload_file({
    file,
    onSuccess: (data: any) => {
      // 构建完整的图片URL
      const imageUrl = data.url;

      // 更新表单数据
      formData.handleImages = [imageUrl];

      // 完成进度
      onProgress?.({ percent: 100 });

      // 调用Upload组件的成功回调
      onSuccess?.(data, file);
    },
    onError: (error: Error) => {
      console.error('上传失败:', error);
      message.error('上传失败');
      onError?.(error);
    },
    onProgress: (progress: any) => {
      // 传递进度信息
      onProgress?.(progress);
    },
  });
};

// 处理上传变化
const handleUploadChange = (info: any) => {
  console.log('上传状态变化:', info);

  // 确保文件列表中的每个文件对象都有正确的结构
  fileList.value = info.fileList.map((file: any) => {
    // 确保文件对象有必要的属性
    return {
      uid: file.uid || `${Date.now()}-${Math.random()}`,
      name: file.name || 'unknown',
      status: file.status || 'uploading',
      url: file.url || file.response?.url,
      percent: typeof file.percent === 'number' ? file.percent : 0,
      response: file.response,
      error: file.error,
      ...file,
    };
  });

  // 如果上传成功，确保表单数据已更新
  if (info.file.status === 'done' && info.file.response) {
    const imageUrl = info.file.response;
    formData.handleImages = [imageUrl];
  }

  // 如果上传失败，清空表单数据
  if (info.file.status === 'error') {
    formData.handleImages = [];
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;
    console.log('提交数据:', formData);

    await updateBusinessAlarm_Api(formData.id, formData);

    message.success('核实成功');

    closeModal();
    emit('success');
  } catch (error) {
    console.error('核实失败', error);
    message.error('核实失败');
  } finally {
    loading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  open: openModal,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    width="800px"
    @cancel="closeModal"
  >
    <!-- 告警详情 -->
    <Descriptions :column="2">
      <DescriptionsItem label="报警类型">
        <a-tag type="warning" >{{ dictStore.getDictLable('BizAlertTypeEnum', detailsData.type) }}</a-tag>
      </DescriptionsItem>
      <DescriptionsItem label="摄像头名称">
        {{ detailsData.cameraName }}
      </DescriptionsItem>
      <DescriptionsItem label="区域">
        {{ detailsData.region }}
      </DescriptionsItem>
      <DescriptionsItem label="场景">
        {{ (detailsData.sceneNames || []).join(',') }}
      </DescriptionsItem>
      <DescriptionsItem label="报警时间">
        {{ formatDate(detailsData.createTime, 'YYYY-MM-DD HH:mm:ss') }}
      </DescriptionsItem>
      <DescriptionsItem label="核实状态">
        <DictTag
          dict-name="VerifyStatusEnum"
          :dict-value="detailsData.verifyStatus"
        />
      </DescriptionsItem>
      <DescriptionsItem label="报警照片" :span="2">
        <a-image
          v-if="detailsData.image"
          :src="mediaUrl + detailsData.image"
          :width="100"
          :preview="true"
        />
        <span v-else>-</span>
      </DescriptionsItem>
      <DescriptionsItem label="报警视频" :span="2">
        <video
          class="video-box"
          v-if="detailsData.video"
          :src="mediaUrl + detailsData.video"
          controls
        ></video>
        <span v-else>-</span>
      </DescriptionsItem>

      <!-- 查看模式显示处理信息 -->
      <template v-if="modalType === 'toView'">
        <DescriptionsItem label="处理状态">
          {{
            dictStore.getDictLable('VerifyStatusEnum', detailsData.verifyStatus)
          }}
        </DescriptionsItem>
        <DescriptionsItem
          label="处理说明"
          v-if="detailsData.verifyStatus === 'REAL'"
        >
          {{ detailsData.handleDesc || '-' }}
        </DescriptionsItem>
        <DescriptionsItem
          label="处理图片"
          v-if="detailsData.verifyStatus === 'REAL'"
        >
          <a-image
            v-if="
              detailsData.handleImages && detailsData.handleImages.length > 0
            "
            :src="mediaUrl + detailsData.handleImages[0]"
            :width="100"
            :preview="true"
          />
          <span v-else>-</span>
        </DescriptionsItem>
        <DescriptionsItem
          label="设置不报警"
          v-if="detailsData.verifyStatus === 'FALSE_ALARM'"
        >
          {{
            detailsData.forbidAlarm === true
              ? '是'
              : detailsData.forbidAlarm === false
                ? '否'
                : '-'
          }}
        </DescriptionsItem>
        <DescriptionsItem
          label="解除不报警时间"
          v-if="detailsData.forbidAlarmTime"
        >
          {{
            detailsData.forbidAlarmTime
              ? formatDate(detailsData.forbidAlarmTime, 'YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </DescriptionsItem>
        <DescriptionsItem label="操作人">
          {{ detailsData.handleName || '-' }}
        </DescriptionsItem>
        <DescriptionsItem label="操作时间">
          {{
            detailsData.handleTime
              ? formatDate(detailsData.handleTime, 'YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </DescriptionsItem>
      </template>
    </Descriptions>

    <!-- 核实表单 -->
    <a-form
      v-if="modalType === 'toCheck'"
      ref="formRef"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :model="formData"
      :rules="formRules"
      labelAlign="left"
    >
      <a-form-item label="处理" name="verifyStatus">
        <a-radio-group v-model:value="formData.verifyStatus">
          <a-radio value="REAL">真实报警</a-radio>
          <a-radio value="FALSE_ALARM">误报</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item v-if="showDescAndImage" label="处理说明" name="handleDesc">
        <a-textarea
          v-model:value="formData.handleDesc"
          placeholder="请输入处理说明"
          :rows="3"
        />
      </a-form-item>

      <a-form-item v-if="showDescAndImage" label="图片" name="handleImages">
        <a-upload
          v-model:file-list="fileList"
          :custom-request="customUpload"
          :max-count="1"
          list-type="picture-card"
          accept=".png,.jpg,.jpeg"
          @change="handleUploadChange"
          :show-upload-list="true"
        >
          <div v-if="fileList.length < 1">
            <div>点击上传</div>
          </div>
        </a-upload>
      </a-form-item>

      <a-form-item v-if="showNoAlarm" label="设置不报警" name="forbidAlarm">
        <a-radio-group v-model:value="formData.forbidAlarm">
          <a-radio :value="true">是</a-radio>
          <a-radio :value="false">否</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item
        v-if="showCancelTime"
        label="解除不报警时间"
        name="forbidAlarmTime"
      >
        <a-date-picker
          v-model:value="formData.forbidAlarmTime"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择解除不报警时间"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button
        v-if="modalType === 'toCheck'"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.video-box {
  width: 172px;
  height: 90px;
  border: 1px solid #8b8b8b;
}
</style>
